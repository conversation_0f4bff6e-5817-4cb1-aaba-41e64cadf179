{"name": "supersplat", "version": "2.6.2", "author": "PlayCanvas<<EMAIL>>", "homepage": "https://playcanvas.com/supersplat/editor", "description": "3D Gaussian Splat Editor", "keywords": ["playcanvas", "ply", "gaussian", "splat", "editor"], "license": "MIT", "main": "index.js", "scripts": {"build": "rollup -c", "watch": "rollup -c -w", "serve": "serve dist -C", "develop": "concurrently --kill-others \"npm run watch\" \"npm run serve\"", "develop:auto-launch": "concurrently --kill-others \"xdg-open 'http://localhost:3000/'\" \"npm run watch\" \"npm run serve\"", "develop:local": "cross-env ENGINE_PATH=../engine npm run develop", "build:local": "cross-env ENGINE_PATH=../engine npm run build", "watch:local": "cross-env ENGINE_PATH=../engine npm run watch", "lint": "eslint src", "postinstall": "npm run install:deps && npm run build:deps", "install:deps": "npm --prefix ./submodules/supersplat-viewer ci", "build:deps": "npm --prefix ./submodules/supersplat-viewer run build"}, "devDependencies": {"@playcanvas/eslint-config": "^2.1.0", "@playcanvas/pcui": "^5.2.0", "@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-image": "^3.0.3", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-strip": "^3.0.4", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@types/wicg-file-system-access": "^2023.10.6", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "autoprefixer": "^10.4.21", "concurrently": "^9.1.2", "cors": "^2.8.5", "cross-env": "^7.0.3", "eslint": "^9.27.0", "eslint-import-resolver-typescript": "^4.3.5", "globals": "^16.1.0", "i18next": "^25.2.0", "i18next-browser-languagedetector": "^8.1.0", "jest": "^29.7.0", "jszip": "^3.10.1", "mp4-muxer": "^5.2.1", "playcanvas": "^2.7.6", "postcss": "^8.5.3", "rollup": "^4.41.0", "rollup-plugin-scss": "^4.0.1", "rollup-plugin-string": "^3.0.0", "rollup-plugin-visualizer": "^5.14.0", "sass": "^1.89.0", "serve": "^14.2.4", "tslib": "^2.8.1", "typescript": "^5.8.3"}}