@use 'colors.scss' as *;

#timeline-panel {
    flex-direction: column;

    &:not(.pcui-hidden) {
        display: flex;
    }

    > #controls-wrap {
        display: flex;
        flex-direction: row;
        background-color: $bcg-primary;
        justify-content: center;
        padding: 1px;

        > #button-controls {
            display: flex;
            flex-direction: row;
            align-items: center;

            > .button {
                font-family: pc-icon;
                font-weight: bold;
                font-size: 13px;

                width: 36px;
                height: 24px;
                margin: 1px;
                padding: 0;
                flex-grow: 0;
                flex-shrink: 0;

                border-radius: 4px;

                text-align: center;
                line-height: 24px;

                &:hover {
                    color: #ff9900;
                    cursor: pointer;
                    box-shadow: none;
                    border-color: #ff990088;
                }
            }
        }

        > .spacer {
            display: flex;
            flex-grow: 1;
            flex-basis: 0;
            justify-content: flex-end;

            > #settings-controls {
                display: flex;
                align-items: center;
                gap: 1px;
                margin-right: 1px;

                > #speed {
                    margin: 0;
                    width: 80px;
                }

                > #totalFrames {
                    margin: 0;
                    width: 80px;
                }
            }
        }
    }

    > #frame-slider {
        height: 24px;
        margin: 0px;

        > .pcui-numeric-input {
            display: none;
            flex-grow: 0;
            margin: 0;
            > input {
                width: 40px;
            }
        }
    }

    > #ticks {
        height: 38px;
        background-color: $bcg-darkest;

        > #ticks-area {
            width: 100%;
            height: 100%;

            > .time-label {
                position: absolute;
                font-size: 12px;
                bottom: 1px;
                transform: translate(-50%, 0);
                padding: 2px;
                pointer-events: none;

                color: $clr-default;

                &.cursor {
                    color: $clr-active;
                    background-color: $clr-hilight;
                    padding: 2px 6px;
                    border-radius: 4px;
                }

                &.key {
                    background-color: $clr-icon-hilight;
                    bottom: 22px;
                    width: 8px;
                    height: 8px;
                    // rectangle shape
                    clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
                    // circle shape
                    // border-radius: 50%;
                    cursor: pointer;
                }
            }
        }
    }
}