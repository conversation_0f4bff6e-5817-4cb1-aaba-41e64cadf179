@use 'colors.scss' as *;

#unicity-preview-page {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: $bcg-primary;
    z-index: 1000;
    display: flex;
    flex-direction: column;

    #preview-toolbar {
        display: flex;
        align-items: center;
        padding: 12px 20px;
        background-color: $bcg-dark;
        border-bottom: 1px solid $bcg-darker;
        min-height: 50px;

        .back-button {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-right: 20px;
            
            .back-icon {
                width: 16px;
                height: 16px;
                transform: rotate(180deg);
            }
        }

        #preview-title {
            flex-grow: 1;
            font-size: 18px;
            font-weight: bold;
            color: $text-primary;
        }

        .publish-button {
            display: flex;
            align-items: center;
            gap: 8px;
            
            .publish-icon {
                width: 16px;
                height: 16px;
            }
        }
    }

    #preview-main-content {
        flex: 1;
        display: flex;
        overflow: hidden;

        #preview-container {
            flex: 1;
            position: relative;
            background-color: $bcg-darker;

            #preview-frame {
                width: 100%;
                height: 100%;
                border: none;
            }
        }

        #preview-control-panel {
            width: 300px;
            background-color: $bcg-primary;
            border-left: 1px solid $bcg-darker;
            padding: 20px;
            overflow-y: auto;

            .control-section {
                margin-bottom: 30px;
                
                .control-label {
                    font-size: 14px;
                    font-weight: bold;
                    color: $text-primary;
                    margin-bottom: 12px;
                    display: block;
                }
            }

            #camera-controls {
                .camera-mode-button {
                    width: 100%;
                    margin-bottom: 8px;
                    background-color: $bcg-dark;
                    border: 1px solid $bcg-darker;
                    color: $text-secondary;
                    
                    &.active {
                        background-color: $clr-hilight;
                        color: white;
                        border-color: $clr-hilight;
                    }
                    
                    &:hover:not(.active) {
                        background-color: $bcg-darker;
                        border-color: $text-secondary;
                    }
                }

                .reset-button {
                    width: 100%;
                    margin-top: 12px;
                    background-color: $bcg-darker;
                    border: 1px solid $bcg-darkest;
                    color: $text-secondary;
                    
                    &:hover {
                        background-color: $bcg-darkest;
                        color: $text-primary;
                    }
                }
            }

            #animation-controls {
                .play-button,
                .pause-button {
                    width: 100%;
                    margin-bottom: 12px;
                    background-color: $clr-hilight;
                    border: 1px solid $clr-hilight;
                    color: white;
                    
                    &:hover {
                        background-color: $bcg-darken;
                    }
                }

                .pcui-slider {
                    width: 100%;
                    margin-bottom: 8px;
                }

                .time-label {
                    font-size: 12px;
                    color: $text-secondary;
                    text-align: center;
                    display: block;
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    #unicity-preview-page {
        #preview-main-content {
            flex-direction: column;

            #preview-control-panel {
                width: 100%;
                height: 200px;
                border-left: none;
                border-top: 1px solid $bcg-darker;
                padding: 15px;

                .control-section {
                    margin-bottom: 20px;
                }

                #camera-controls {
                    .camera-mode-button {
                        display: inline-block;
                        width: calc(33.33% - 4px);
                        margin-right: 6px;
                        margin-bottom: 8px;
                        
                        &:nth-child(3n) {
                            margin-right: 0;
                        }
                    }

                    .reset-button {
                        width: 100%;
                        margin-top: 8px;
                    }
                }
            }
        }

        #preview-toolbar {
            padding: 8px 15px;
            
            #preview-title {
                font-size: 16px;
            }
            
            .back-button,
            .publish-button {
                font-size: 12px;
                padding: 6px 12px;
            }
        }
    }
}

// 按钮通用样式
.button {
    padding: 0px 16px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    outline: none;
    
    &:focus {
        box-shadow: 0 0 0 2px rgba($clr-hilight, 0.3);
    }
    
    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
    
    &.primary {
        background-color: $clr-hilight;
        color: white;
        
        &:hover:not(:disabled) {
            background-color: $bcg-darken;
        }
    }
}

// 加载状态
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: $text-secondary;
    
    &::after {
        content: '';
        width: 20px;
        height: 20px;
        border: 2px solid $bcg-darker;
        border-top: 2px solid $clr-hilight;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-left: 10px;
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
