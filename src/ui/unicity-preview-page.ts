import { Button, Container, Element, Label, SliderInput } from 'pcui';

import { Events } from '../events';
import { localize } from './localization';
import { UnicityPublishSettings } from '../unicity-service';
import scenePublish from './svg/publish.svg';
import backIcon from './svg/arrow.svg';

const createSvg = (svgString: string, args = {}) => {
    const decodedStr = decodeURIComponent(svgString.substring('data:image/svg+xml,'.length));
    return new Element({
        dom: new DOMParser().parseFromString(decodedStr, 'image/svg+xml').documentElement,
        ...args
    });
};

/**
 * Unicity预览页面 - 独立页面版本
 */
class UnicityPreviewPage extends Container {
    private previewFrame: HTMLIFrameElement;
    private publishSettings: UnicityPublishSettings;
    private viewerState: any;
    private events: Events;
    private publishButton: Button;
    private timelineSlider: SliderInput;
    private timeLabel: Label;
    private isUpdatingSlider: boolean = false;

    constructor(events: Events, args = {}) {
        args = {
            id: 'unicity-preview-page',
            hidden: true,
            ...args
        };

        super(args);

        this.events = events;

        // 顶部工具栏
        const toolbar = new Container({
            id: 'preview-toolbar'
        });

        // 返回按钮
        const backButton = new Button({
            class: ['button', 'back-button'],
            text: localize('unicity.back-to-edit')
        });

        backButton.dom.prepend(createSvg(backIcon, { class: 'back-icon' }).dom);

        // 标题
        const title = new Label({
            id: 'preview-title',
            text: localize('unicity.preview')
        });

        // 发布按钮
        this.publishButton = new Button({
            class: ['button', 'primary', 'publish-button'],
            text: localize('unicity.publish')
        });

        this.publishButton.dom.prepend(createSvg(scenePublish, { class: 'publish-icon' }).dom);

        toolbar.append(backButton);
        toolbar.append(title);
        toolbar.append(this.publishButton);

        // 主要内容区域
        const mainContent = new Container({
            id: 'preview-main-content'
        });

        // 预览区域
        const previewContainer = new Container({
            id: 'preview-container'
        });

        // 创建iframe用于预览
        this.previewFrame = document.createElement('iframe');
        this.previewFrame.id = 'preview-frame';
        this.previewFrame.style.width = '100%';
        this.previewFrame.style.height = '100%';
        this.previewFrame.style.border = 'none';

        const previewElement = new Element({
            dom: this.previewFrame
        });

        previewContainer.append(previewElement);

        // 控制面板
        const controlPanel = new Container({
            id: 'preview-control-panel'
        });

        // 相机控制
        const cameraControls = new Container({
            id: 'camera-controls',
            class: 'control-section'
        });

        const cameraLabel = new Label({
            text: localize('camera.controls'),
            class: 'control-label'
        });

        const orbitButton = new Button({
            class: ['button', 'camera-mode-button', 'active'],
            text: localize('camera.orbit'),
            id: 'orbit-mode-btn'
        });

        const flyButton = new Button({
            class: ['button', 'camera-mode-button'],
            text: localize('camera.fly'),
            id: 'fly-mode-btn'
        });

        const animButton = new Button({
            class: ['button', 'camera-mode-button'],
            text: localize('camera.animation'),
            id: 'anim-mode-btn'
        });

        const resetButton = new Button({
            class: ['button', 'reset-button'],
            text: localize('camera.reset')
        });

        cameraControls.append(cameraLabel);
        cameraControls.append(orbitButton);
        cameraControls.append(flyButton);
        cameraControls.append(animButton);
        cameraControls.append(resetButton);

        // 动画控制
        const animationControls = new Container({
            id: 'animation-controls',
            class: 'control-section',
            hidden: true
        });

        const animationLabel = new Label({
            text: localize('animation.controls'),
            class: 'control-label'
        });

        const playButton = new Button({
            class: ['button', 'play-button'],
            text: localize('animation.play')
        });

        const pauseButton = new Button({
            class: ['button', 'pause-button'],
            text: localize('animation.pause'),
            hidden: true
        });

        this.timelineSlider = new SliderInput({
            min: 0,
            max: 100,
            value: 0,
            precision: 2
        });

        this.timeLabel = new Label({
            text: '0.00s',
            class: 'time-label'
        });

        animationControls.append(animationLabel);
        animationControls.append(playButton);
        animationControls.append(pauseButton);
        animationControls.append(this.timelineSlider);
        animationControls.append(this.timeLabel);

        controlPanel.append(cameraControls);
        controlPanel.append(animationControls);

        mainContent.append(previewContainer);
        mainContent.append(controlPanel);

        this.append(toolbar);
        this.append(mainContent);

        // 初始化viewer状态
        this.viewerState = {
            cameraMode: 'orbit',
            animationTime: 0,
            animationDuration: 0,
            animationPaused: true,
            animationPlaying: false,
            hasAnimation: false
        };

        // 事件处理
        backButton.on('click', async () => {
            console.log('Back button clicked');
            // 直接调用页面路由器返回编辑器
            await this.events.invoke('show.editorPage');
        });

        this.publishButton.on('click', async () => {
            console.log('Publish button clicked');
            // 执行发布流程
            if (this.publishSettings) {
                await this.events.invoke('scene.publishUnicity', this.publishSettings);
                // 发布完成后返回编辑器
                await this.events.invoke('show.editorPage');
            }
        });

        // 相机模式切换
        const cameraButtons = [orbitButton, flyButton, animButton];
        const cameraModes = ['orbit', 'fly', 'anim'];

        cameraButtons.forEach((button, index) => {
            button.on('click', () => {
                const mode = cameraModes[index];

                if (mode === 'anim') {
                    // 动画模式：显示动画控制，但不改变相机控制模式
                    animationControls.hidden = false;
                    // 如果有动画，开始播放
                    if (this.viewerState.hasAnimation) {
                        this.viewerState.animationPaused = false;
                        playButton.hidden = true;
                        pauseButton.hidden = false;
                        this.sendMessageToViewer('playAnimation');
                    }
                } else {
                    // orbit/fly模式：切换相机控制，隐藏动画控制
                    cameraButtons.forEach(btn => btn.class.remove('active'));
                    button.class.add('active');

                    this.viewerState.cameraMode = mode;
                    this.sendMessageToViewer('setCameraMode', mode);
                    animationControls.hidden = true;

                    // 停止动画播放
                    this.viewerState.animationPaused = true;
                    playButton.hidden = false;
                    pauseButton.hidden = true;
                    this.sendMessageToViewer('pauseAnimation');
                }
            });
        });

        // 重置按钮
        resetButton.on('click', () => {
            this.sendMessageToViewer('reset');
        });

        // 动画控制
        playButton.on('click', () => {
            this.viewerState.animationPaused = false;
            playButton.hidden = true;
            pauseButton.hidden = false;
            this.sendMessageToViewer('playAnimation');

            // 显示动画控制面板
            animationControls.hidden = false;
        });

        pauseButton.on('click', () => {
            this.viewerState.animationPaused = true;
            playButton.hidden = false;
            pauseButton.hidden = true;
            this.sendMessageToViewer('pauseAnimation');
        });

        // 时间轴控制
        this.timelineSlider.on('change', (value: number) => {
            if (this.isUpdatingSlider) return; // 避免循环触发

            const time = (value / 100) * this.viewerState.animationDuration;
            this.viewerState.animationTime = time;
            this.timeLabel.text = `${time.toFixed(2)}s`;
            this.sendMessageToViewer('setAnimationTime', time);
        });

        // 监听来自viewer的消息
        window.addEventListener('message', (event) => {
            if (event.source === this.previewFrame.contentWindow) {
                this.handleViewerMessage(event.data);
            }
        });

    }

    // 显示预览页面
    async show(): Promise<void> {
        // 获取发布设置
        this.publishSettings = await this.events.invoke('unicity.getPublishSettings');

        if (!this.publishSettings) {
            console.error('Failed to get publish settings');
            return;
        }

        const projectId = await this.events.invoke('unicity.getProjectId');
        if (!projectId) {
            this.publishButton.enabled = false;
        }

        // 生成预览内容
        await this.generatePreview();

        //this.hidden = false;
    }

    // 生成预览内容
    async generatePreview(): Promise<void> {
        // 创建增强的预览HTML，包含viewer功能
        const previewHtml = await this.events.invoke('scene.generateUnicityPreviewWithViewer', this.publishSettings);

        // 将预览数据加载到iframe中
        const blob = new Blob([previewHtml], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        this.previewFrame.src = url;

        // 在iframe加载完成后释放URL
        this.previewFrame.onload = () => {
            URL.revokeObjectURL(url);
            // 初始化viewer状态
            this.initializeViewer();
        };
    }

    // 隐藏预览页面
    hide(): void {
        this.dom.style.visibility = 'hidden';
        //this.hidden = true;
        // 清空iframe内容
        this.previewFrame.src = 'about:blank';
    }

    // 销毁预览页面
    destroy(): void {
        this.hide();
        super.destroy();
    }

    // 向viewer发送消息
    private sendMessageToViewer(type: string, data?: any) {
        if (this.previewFrame.contentWindow) {
            this.previewFrame.contentWindow.postMessage({ type, data }, '*');
        }
    }

    // 处理来自viewer的消息
    private handleViewerMessage(message: any) {
        switch (message.type) {
            case 'viewerReady':
                this.initializeViewer();
                break;
            case 'animationInfo':
                this.viewerState.hasAnimation = message.data.hasAnimation;
                this.viewerState.animationDuration = message.data.duration;
                console.log('收到动画信息:', message.data);
                // 更新UI状态
                this.updateAnimationControls();
                break;
            case 'animationTimeUpdate':
                this.viewerState.animationTime = message.data.time;
                this.updateTimeDisplay();
                break;
        }
    }

    // 初始化viewer
    private initializeViewer() {
        this.sendMessageToViewer('initialize', {
            cameraMode: this.viewerState.cameraMode,
            animationPaused: this.viewerState.animationPaused
        });
    }

    // 更新动画控制UI
    private updateAnimationControls() {
        const animationControls = this.dom.querySelector('#animation-controls') as any;
        if (animationControls) {
            // 如果有动画，显示动画按钮，但默认隐藏控制面板
            const animButton = this.dom.querySelector('#anim-mode-btn') as any;
            if (animButton) {
                animButton.ui.hidden = !this.viewerState.hasAnimation;
            }

            // 动画控制面板默认隐藏，只有在播放动画时才显示
            animationControls.ui.hidden = true;
        }
    }

    // 更新时间显示
    private updateTimeDisplay() {
        // 更新时间标签
        if (this.timeLabel) {
            this.timeLabel.text = `${this.viewerState.animationTime.toFixed(2)}s`;
        }

        // 更新时间轴滑块
        if (this.timelineSlider && this.viewerState.animationDuration > 0) {
            const percentage = (this.viewerState.animationTime / this.viewerState.animationDuration) * 100;

            // 使用标志位避免循环触发
            //this.isUpdatingSlider = true;
            this.timelineSlider.value = percentage;
            //this.isUpdatingSlider = false;
        }
    }
}

export { UnicityPreviewPage };
