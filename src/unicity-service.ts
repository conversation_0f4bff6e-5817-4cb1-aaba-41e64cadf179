import { ExperienceSettings } from './splat-serialize';
import { localize } from './ui/localization';

const HOST = "https://api.unicity.dev";
const BASE_URL = `/api/v1/projects`;

/**
 * Unicity云服务的用户信息
 */
export type UnicityUser = {
    id: string;
    token: string;
    name?: string;
    email?: string;
};

/**
 * Unicity项目信息
 */
export type UnicityProject = {
    id: string;
    title: string;
    createdAt: string;
    updatedAt: string;
    ownerId: string;
    isPublic: boolean;
    viewUrl?: string;
};

/**
 * Unicity发布设置
 */
export type UnicityPublishSettings = {
    projectId: string;
    title?: string;
    isPublic?: boolean;
    serializeSettings: {
        maxSHBands: number;
        minOpacity?: number;
        removeInvalid?: boolean;
    };
    experienceSettings: ExperienceSettings;
};

/**
 * 上传文件的结果
 */
export type UnicityUploadResult = {
    url: string;
    id: string;
    title: string;
    isPublic: boolean;
    createdAt: string;
    updatedAt: string;
};

/**
 * 项目文件类型
 */
export enum UnicityFileType {
    SPLAT = 'splat',
    SETTINGS = 'settings'
}

/**
 * 本地存储键
 */
const STORAGE_KEYS = {
    PROJECTS: 'unicity_mock_projects',
    USER: 'unicity_mock_user'
};

/**
 * 本地开发者用户假数据
 * 用于开发和测试
 */
const MOCK_LOCAL_USER: UnicityUser = {
    id: 'dev-123456789',
    token: 'mock-jwt-token-for-local-development-1234567890',
    name: '本地开发者',
    email: '<EMAIL>'
};

/**
 * 从本地存储获取模拟项目列表
 */
const getMockProjects = (): UnicityProject[] => {
    try {
        const projectsJson = localStorage.getItem(STORAGE_KEYS.PROJECTS);
        return projectsJson ? JSON.parse(projectsJson) : [];
    } catch (e) {
        console.error('获取模拟项目列表失败:', e);
        return [];
    }
};

/**
 * 保存模拟项目列表到本地存储
 */
const saveMockProjects = (projects: UnicityProject[]): void => {
    try {
        localStorage.setItem(STORAGE_KEYS.PROJECTS, JSON.stringify(projects));
    } catch (e) {
        console.error('保存模拟项目列表失败:', e);
    }
};

/**
 * 打开IndexedDB数据库
 */
const openDatabase = (): Promise<IDBDatabase> => {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('UnicityMockDB', 1);

        request.onerror = (event) => {
            reject(new Error('无法打开IndexedDB数据库'));
        };

        request.onupgradeneeded = (event) => {
            const db = request.result;
            // 创建存储项目文件的对象存储
            if (!db.objectStoreNames.contains('projectFiles')) {
                db.createObjectStore('projectFiles', { keyPath: 'id' });
            }
        };

        request.onsuccess = (event) => {
            resolve(request.result);
        };
    });
};

/**
 * 保存模拟项目文件
 */
const saveMockProjectFile = async (projectId: string, fileType: UnicityFileType, data: Uint8Array | string): Promise<void> => {
    try {
        const key = `${STORAGE_KEYS.PROJECTS}_${projectId}_${fileType}`;

        if (fileType === UnicityFileType.SPLAT && data instanceof Uint8Array) {
            // 对于二进制数据，使用IndexedDB存储
            const db = await openDatabase();
            const transaction = db.transaction(['projectFiles'], 'readwrite');
            const store = transaction.objectStore('projectFiles');

            // 存储数据
            const storeRequest = store.put({
                id: key,
                data: data
            });

            await new Promise<void>((resolve, reject) => {
                storeRequest.onsuccess = () => resolve();
                storeRequest.onerror = () => reject(storeRequest.error);
                transaction.oncomplete = () => db.close();
            });

            // 在localStorage中存储一个标记，表示数据在IndexedDB中
            localStorage.setItem(key, 'STORED_IN_INDEXEDDB');
        } else {
            // 对于JSON数据，直接存储字符串到localStorage
            localStorage.setItem(key, data as string);
        }
    } catch (e) {
        console.error(`保存模拟项目文件失败 (${fileType}):`, e);

        // 如果是大文件，提示用户
        if (e.name === 'QuotaExceededError') {
            console.warn('文件太大，无法保存到本地存储。将只下载文件而不保存到本地存储。');
        }
    }
};

/**
 * 检查用户是否已登录Unicity服务
 *
 * 开发模式：返回本地开发者用户假数据
 * 生产模式：调用实际的API
 */
export const getUnicityUser = async (): Promise<UnicityUser | null> => {
    // 开发阶段：始终返回本地开发者用户假数据，不进行实际的用户鉴权
    console.log('开发阶段：使用本地开发者用户假数据');
    return MOCK_LOCAL_USER;

    // 生产模式代码（暂时注释掉）
    // try {
    //     // 这里应该替换为实际的Unicity API端点
    //     const response = await fetch('https://api.unicity.com/auth/user');
    //     if (!response.ok) {
    //         return null;
    //     }
    //     return await response.json() as UnicityUser;
    // } catch (e) {
    //     console.error('Failed to get Unicity user:', e);
    //     return null;
    // }
};

/**
 * 上传文件到Unicity云服务
 * @param splatData .splat文件的二进制数据
 * @param settingsData settings.json的内容
 * @param publishSettings 发布设置
 * @param user 用户信息
 */
export const uploadToUnicity = async (
    splatData: Uint8Array,
    settingsData: string,
    publishSettings: UnicityPublishSettings,
    user: UnicityUser
): Promise<UnicityUploadResult> => {
    // 开发模式：模拟上传过程
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1' || window.location.hostname.includes('.local') || user === MOCK_LOCAL_USER) {
        console.log('模拟上传到Unicity云服务');
        console.log(`标题: ${publishSettings.title}`);
        console.log(`项目ID: ${publishSettings.projectId || '新项目'}`);
        console.log(`Splat数据大小: ${splatData.byteLength} 字节`);
        console.log(`Settings数据: ${settingsData.substring(0, 100)}...`);

        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 1500));

        // 获取现有项目列表
        const projects = getMockProjects();
        let project: UnicityProject;
        const now = new Date().toISOString();

        // 更新现有项目
        const existingProjectIndex = projects.findIndex(p => p.id === publishSettings.projectId);
        if (existingProjectIndex > -1) {
            project = projects[existingProjectIndex];
            project.title = publishSettings.title;
            project.isPublic = publishSettings.isPublic ?? project.isPublic;
            project.updatedAt = now;

            // 更新项目列表
            projects[existingProjectIndex] = project;
        } else {
            // 创建新项目
            project = {
                id: `mock-project-${Date.now()}`,
                title: publishSettings.title,
                createdAt: now,
                updatedAt: now,
                ownerId: user.id,
                isPublic: publishSettings.isPublic ?? false
            };
            projects.push(project);
        }

        // 保存项目列表
        saveMockProjects(projects);

        // 保存项目文件
        try {
            await saveMockProjectFile(project.id, UnicityFileType.SPLAT, splatData);
            await saveMockProjectFile(project.id, UnicityFileType.SETTINGS, settingsData);
        } catch (storageError) {
            console.warn('保存项目文件到本地存储失败，但将继续下载文件:', storageError);
        }

        // 创建本地文件下载
        // try {
        //     // 下载 .splat 文件
        //     const splatBlob = new Blob([splatData], { type: 'application/octet-stream' });
        //     const splatUrl = URL.createObjectURL(splatBlob);
        //     const splatLink = document.createElement('a');
        //     splatLink.href = splatUrl;
        //     splatLink.download = `${publishSettings.title.replace(/\s+/g, '_')}.splat`;
        //     document.body.appendChild(splatLink);
        //     splatLink.click();
        //     document.body.removeChild(splatLink);
        //     URL.revokeObjectURL(splatUrl);

        //     // 下载 settings.json 文件
        //     const settingsBlob = new Blob([settingsData], { type: 'application/json' });
        //     const settingsUrl = URL.createObjectURL(settingsBlob);
        //     const settingsLink = document.createElement('a');
        //     settingsLink.href = settingsUrl;
        //     settingsLink.download = 'settings.json';
        //     document.body.appendChild(settingsLink);
        //     settingsLink.click();
        //     document.body.removeChild(settingsLink);
        //     URL.revokeObjectURL(settingsUrl);

        //     console.log('本地文件已创建');
        // } catch (e) {
        //     console.error('创建本地文件失败:', e);
        // }

        // 返回模拟的上传结果
        return {
            url: project.viewUrl || `https://viewer.unicity.dev/${publishSettings.title?.replace(/\s+/g, '-').toLowerCase()}`,
            id: project.id,
            title: project.title,
            isPublic: project.isPublic,
            createdAt: project.createdAt,
            updatedAt: project.updatedAt
        };
    }

    // 生产模式：实际上传过程
    // 只支持更新现有项目，必须提供projectId
    if (!publishSettings.projectId) {
        throw new Error('必须选择一个现有项目进行发布');
    }

    try {
        // 1. 上传splat和settings.json文件
        const formData = new FormData();
        formData.append('splat', new Blob([splatData], { type: 'application/octet-stream' }), 'model.splat');
        formData.append('settings', new Blob([settingsData], { type: 'application/json' }), 'settings.json');

        const uploadResponse = await fetch(
            `${HOST}${BASE_URL}/upload`, {
            method: 'POST', // 通常使用 POST 上传多个文件
            body: formData
        });

        if (!uploadResponse.ok) {
            throw new Error('上传文件失败');
        }

        const urlData = await uploadResponse.json();

        // 2. 通知服务器上传完成
        const publishResponse = await fetch(
            `${HOST}${BASE_URL}/${publishSettings.projectId}/update`, {
            method: 'POST',
            body: JSON.stringify({
                splat: urlData.data.splatUrl,
                settings: urlData.data.settingsUrl
            }),
            headers: {
                'Content-Type': 'application/json'
                // 开发阶段：暂时注释掉用户鉴权
                // 'Authorization': `Bearer ${user.token}`
            }
        });

        const publishResponseData = await publishResponse.json();

        if (!publishResponse.ok) {
            let msg = '发布失败';
            msg = publishResponseData.statusText;
            throw new Error(msg);
        }

        return publishResponseData.data;
    } catch (err) {
        console.error('上传到Unicity云服务失败:', err);
        throw err;
    }
};
